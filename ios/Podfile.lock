PODS:
  - appsflyer_sdk (6.15.3):
    - Apps<PERSON>lyerFramework (= 6.15.3)
    - Flutter
  - AppsFlyerFramework (6.15.3):
    - AppsFlyerFramework/Main (= 6.15.3)
  - AppsFlyerFramework/Main (6.15.3)
  - device_info_plus (0.0.1):
    - Flutter
  - file_saver (0.0.1):
    - Flutter
  - Firebase/Analytics (11.8.0):
    - Firebase/Core
  - Firebase/Core (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Crashlytics (11.8.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.8.0)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - Firebase/RemoteConfig (11.8.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.8.0)
  - firebase_analytics (11.4.4):
    - Firebase/Analytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.1):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_crashlytics (4.3.4):
    - Firebase/Crashlytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.4):
    - Firebase/Messaging (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.2):
    - Firebase/RemoteConfig (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseAnalytics (11.8.0):
    - FirebaseAnalytics/AdIdSupport (= 11.8.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.8.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.10.0)
  - FirebaseSessions (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.10.0)
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - freshchat_sdk (0.10.25):
    - Flutter
    - FreshchatSDK (= 6.3.7)
  - FreshchatSDK (6.3.7)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GoogleAppMeasurement (11.8.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.8.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.8.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_gallery_saver (2.0.2):
    - Flutter
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Mixpanel-swift (4.4.0):
    - Mixpanel-swift/Complete (= 4.4.0)
  - Mixpanel-swift/Complete (4.4.0)
  - mixpanel_flutter (2.4.0):
    - Flutter
    - Mixpanel-swift (= 4.4.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - Onfido (32.4.0)
  - onfido_sdk (0.0.1):
    - Flutter
    - Onfido (~> 32.4.0)
  - open_file_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - store_redirect (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - freshchat_sdk (from `.symlinks/plugins/freshchat_sdk/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - onfido_sdk (from `.symlinks/plugins/onfido_sdk/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppsFlyerFramework
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FreshchatSDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - Mixpanel-swift
    - nanopb
    - Onfido
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  freshchat_sdk:
    :path: ".symlinks/plugins/freshchat_sdk/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  onfido_sdk:
    :path: ".symlinks/plugins/onfido_sdk/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  appsflyer_sdk: 0132c7cd33db42ce3daf878f02809a9f88756f9f
  AppsFlyerFramework: ad7ff0d22aa36c7f8cc4f71a5424e19b89ccb8ae
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  file_saver: 503e386464dbe118f630e17b4c2e1190fa0cf808
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_analytics: e3b6782e70e32b7fa18f7cd233e3201975dd86aa
  firebase_core: ac395f994af4e28f6a38b59e05a88ca57abeb874
  firebase_crashlytics: 0f3a46f30fce2159f715ac33eef98a2aa5d598f1
  firebase_messaging: 7e223f4ee7ca053bf4ce43748e84a6d774ec9728
  firebase_remote_config: a98909ec3fd3aef7a128d5a507181c82abc8694e
  FirebaseABTesting: 7d6eee42b9137541eac2610e5fea3568d956707a
  FirebaseAnalytics: 4fd42def128146e24e480e89f310e3d8534ea42b
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseCrashlytics: a1102c035f18d5dd94a5969ee439c526d0c9e313
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  FirebaseRemoteConfig: f63724461fd97f0d62f20021314b59388f3e8ef8
  FirebaseRemoteConfigInterop: 7c9a9c65eff32cbb0f7bf8d18140612ad57dfcc6
  FirebaseSessions: c4d40a97f88f9eaff2834d61b4fea0a522d62123
  FirebaseSharedSwift: 1baacae75939499b5def867cbe34129464536a38
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_contacts: edb1c5ce76aa433e20e6cb14c615f4c0b66e0983
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  freshchat_sdk: 4078d62c19b5f0163c2398b6e27600d6654ba7b9
  FreshchatSDK: 52a5fbda55f5892cc96bc0f2cd80ffe188fa106c
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  GoogleAppMeasurement: fc0817122bd4d4189164f85374e06773b9561896
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  Mixpanel-swift: 478ff46d19de4a251244a9c9a582070d4bb94cf9
  mixpanel_flutter: c2e55a95a2ae23cf27c065115ecee6226e0e0718
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  Onfido: 60a53fd00902511f0d66f890329c7734081f208f
  onfido_sdk: 43533125915d70b2ff2f79512b22ff5f8ab4067d
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 1959d098c91d8a792531a723c4a9d7e9f6a01e38

COCOAPODS: 1.16.2
