import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateFeedbackModal extends StatefulWidget {
  const RateFeedbackModal({super.key});

  @override
  State<RateFeedbackModal> createState() => _RateFeedbackModalState();
}

class _RateFeedbackModalState extends State<RateFeedbackModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(24),
            Text(
              'Your feedback helps us improve Korrency for everyone',
              textAlign: TextAlign.center,
              style: FontTypography.text20.semiBold,
            ),
            YBox(4),
            Text(
              'Take a moment to tell us how we can improve',
              textAlign: TextAlign.center,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray93,
              ),
            ),
            const YBox(24),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Your Feedback',
                textAlign: TextAlign.center,
                style: FontTypography.text14.withCustomColor(
                  AppColors.gray51,
                ),
              ),
            ),
            const YBox(6),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grayAB),
                borderRadius: BorderRadius.circular(Sizer.height(12)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: Sizer.width(12),
                      top: Sizer.height(10),
                    ),
                    child: Icon(
                      Iconsax.message_edit,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  Expanded(
                    child: CustomTextField(
                      // controller: answerControllers[index],
                      hideBorder: true,
                      hintText: "Start typing here...",
                      borderRadius: Sizer.height(12),
                      maxLines: 3,
                      contentPadding: EdgeInsets.only(
                        left: Sizer.width(8),
                        top: Sizer.height(20),
                      ),
                      onChanged: (val) {
                        // Validation is handled by the controller listener
                      },
                    ),
                  ),
                ],
              ),
            ),
            const YBox(28),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                BsWrapper.bottomSheet(
                  context: context,
                  widget: const RateSuccessModal(),
                );
              },
              text: "Submit",
            ),
            const YBox(36),
          ],
        ),
      ),
    );
  }
}
