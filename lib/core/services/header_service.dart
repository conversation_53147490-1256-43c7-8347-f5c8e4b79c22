import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get_ip_address/get_ip_address.dart';
import 'package:korrency/core/core.dart';

class HeaderService {
  String? deviceId;
  String? deviceType;
  String? deviceName;
  String? notification;

  getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      printty("ios_______");
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      printty("Android device id ${iosInfo.identifierForVendor}.");
      printty("Android device deviceType ${iosInfo.model}.");
      printty("Android device deviceName ${iosInfo.name}.");
      printty("Android device deviceModel ${iosInfo.model}.");

      StorageService.storeStringItem(
          StorageKey.deviceId, iosInfo.identifierForVendor ?? "");
      StorageService.storeStringItem(StorageKey.deviceType, "IOS");
      StorageService.storeStringItem(StorageKey.deviceName, iosInfo.name);
      StorageService.storeStringItem(StorageKey.deviceModel, iosInfo.model);
    }
    if (Platform.isAndroid) {
      printty("andriod_______");
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      printty("Android device id ${androidInfo.id}.");
      printty("Android device deviceType ${androidInfo.model}.");
      printty("Android device deviceName ${androidInfo.name}.");
      printty("Android device deviceModel ${androidInfo.model}.");
      StorageService.storeStringItem(StorageKey.deviceId, androidInfo.id);
      StorageService.storeStringItem(StorageKey.deviceType, "Android");
      StorageService.storeStringItem(StorageKey.deviceName, androidInfo.name);
      StorageService.storeStringItem(StorageKey.deviceModel, androidInfo.model);
    }
    try {
      /// Initialize Ip Address
      var ipAddress = IpAddress(type: RequestType.json);

      /// Get the IpAddress based on requestType.
      dynamic data = await ipAddress.getIpAddress();
      StorageService.storeStringItem(StorageKey.ipAddress, data["ip"]);
    } on IpAddressException catch (_) {}
  }
}

class FCMTokenService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  Future<String?> getToken() async {
    try {
      // Always get a fresh token from Firebase
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        // Store the token
        await StorageService.storeStringItem(StorageKey.deviceToken, token);
        printty("FCM token retrieved and stored: $token");
      } else {
        printty("Warning: Firebase returned null token");
      }
      return token;
    } catch (e) {
      printty("Error getting FCM token: $e");
      return null;
    }
  }

  void setupTokenRefreshListener() {
    // Listen for token refreshes
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      printty("FCM token refreshed: $newToken");
      // Store the new token
      await StorageService.storeStringItem(StorageKey.deviceToken, newToken);

      // Here you could also send the new token to your backend
      // This ensures your backend always has the latest token
      _updateTokenOnBackend(newToken);
    }).onError((err) {
      printty("FCM token refresh error: $err");
    });
  }

  Future<void> _updateTokenOnBackend(String token) async {
    // This method would call your backend API to update the token
    // Implement according to your backend API requirements
    try {
      // Use the existing apiService instance
      await apiService.postWithAuth(
        url: "/auth/update-device-token",
        body: {"device_token": token},
      );
      printty("FCM token updated on backend");
    } catch (e) {
      printty("Error updating FCM token on backend: $e");
    }
  }
}
