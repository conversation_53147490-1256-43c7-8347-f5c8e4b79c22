import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateSuccessModal extends StatefulWidget {
  const RateSuccessModal({super.key});

  @override
  State<RateSuccessModal> createState() => _RateSuccessModalState();
}

class _RateSuccessModalState extends State<RateSuccessModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(80),
                Text(
                  'Thanks for your feedback',
                  textAlign: TextAlign.center,
                  style: FontTypography.text20.semiBold,
                ),
                YBox(4),
                Text(
                  'We have received your feedback and would work on it, Thank you!',
                  textAlign: TextAlign.center,
                  style: FontTypography.text14.withCustomColor(
                    AppColors.gray93,
                  ),
                ),
                const YBox(28),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    Navigator.pop(context);
                    BsWrapper.bottomSheet(
                      context: context,
                      widget: const RateErrorModal(),
                    );
                  },
                  text: "Done",
                ),
                const YBox(36),
              ],
            ),
          ),
          Positioned(
            top: -Sizer.height(62),
            right: 0,
            left: 0,
            child: imageHelper(
              AppImages.ratingHeart,
              height: Sizer.height(124),
              width: Sizer.width(124),
            ),
          )
        ],
      ),
    );
  }
}
