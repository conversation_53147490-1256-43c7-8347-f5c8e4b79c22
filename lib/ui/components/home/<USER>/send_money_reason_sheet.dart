import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyReasonSheet extends StatefulWidget {
  const SendMoneyReasonSheet({
    super.key,
  });

  @override
  State<SendMoneyReasonSheet> createState() => _SendMoneyReasonSheetState();
}

class _SendMoneyReasonSheetState extends State<SendMoneyReasonSheet> {
  SendMoneyReason? selectedReason;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TransactionVM>().getTransactionPurposes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        margin: EdgeInsets.only(
          top: Sizer.height(80),
        ),
        child: ContainerWithTopBorderRadius(
          height: Sizer.screenHeight * 0.6,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(20),
              Row(
                children: [
                  Text(
                    'Select a reason',
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: const Icon(
                      Icons.close,
                      size: 24,
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Builder(builder: (context) {
                  if (vm.isBusy) {
                    return SizedBox(
                      height: Sizer.height(400),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }
                  if (vm.transactionReasons.isEmpty) {
                    return SizedBox(
                      height: Sizer.height(400),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Iconsax.box,
                              size: Sizer.radius(40),
                            ),
                            const YBox(10),
                            Text(
                              "No Occupation Found",
                              style: AppTypography.text16.copyWith(
                                color: AppColors.textBlack600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                  return ListView(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(
                      top: Sizer.height(10),
                    ),
                    children: [
                      const YBox(24),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (ctx, i) {
                          final reason = vm.transactionReasons[i];
                          return InkWell(
                            onTap: () {
                              selectedReason = reason;
                              vm.setSelectedReason(reason);
                              Navigator.pop(context, reason);
                            },
                            child: ContainerWithBluewishBg(
                              padding: EdgeInsets.symmetric(
                                vertical: Sizer.height(12),
                                horizontal: Sizer.width(16),
                              ),
                              bgColor: AppColors.blueFA,
                              child: WalletListTile(
                                title: reason.name ?? "",
                                titleFontSize: 14,
                                trailingIconSize: 16,
                                showTrailing: true,
                                titleMaxLines: 2,
                                trailingWidget: (vm.selectedReason == reason) ||
                                        (selectedReason == reason)
                                    ? const Icon(
                                        Icons.check_circle,
                                        size: 26,
                                        color: AppColors.baseGreen,
                                      )
                                    : Container(
                                        width: Sizer.width(20),
                                        height: Sizer.height(20),
                                        decoration: BoxDecoration(
                                          // color: AppColors.white,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          border: Border.all(
                                            color: AppColors.grayBF,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (ctx, _) => const YBox(16),
                        itemCount: vm.transactionReasons.length,
                      ),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      );
    });
  }
}
