import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateExperienceModal extends StatefulWidget {
  const RateExperienceModal({super.key});

  @override
  State<RateExperienceModal> createState() => _RateExperienceModalState();
}

class _RateExperienceModalState extends State<RateExperienceModal> {
  final _emojis = [
    AppImages.emoji1,
    AppImages.emoji2,
    AppImages.emoji3,
    AppImages.emoji4,
    AppImages.emoji5,
  ];

  int _selectedEmoji = -1;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(80),
                Text(
                  'How would you rate your \nexperience',
                  textAlign: TextAlign.center,
                  style: FontTypography.text22.semiBold,
                ),
                YBox(4),
                Text(
                  'We would love to hear from you',
                  textAlign: TextAlign.center,
                  style: FontTypography.text16.withCustomColor(
                    AppColors.gray93,
                  ),
                ),
                const YBox(37),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    _emojis.length,
                    (i) => InkWell(
                      onTap: () {
                        _selectedEmoji = i;
                        setState(() {});
                        Navigator.pop(context);
                        // if i is between 0 to 2 show negative feedback modal
                        // else show positive feedback modal
                        if (i < 3) {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const RateFeedbackModal(),
                          );
                        } else {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const RateFeedbackModal(),
                          );
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.all(Sizer.radius(8)),
                        decoration: BoxDecoration(
                          color: _selectedEmoji == i
                              ? AppColors.blueE5
                              : AppColors.blueFD,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: imageHelper(
                          _emojis[i],
                          height: Sizer.height(38),
                          width: Sizer.width(38),
                        ),
                      ),
                    ),
                  ),
                ),
                const YBox(50),
              ],
            ),
          ),
          Positioned(
            top: -Sizer.height(62),
            right: 0,
            left: 0,
            child: imageHelper(
              AppImages.star,
              height: Sizer.height(124),
              width: Sizer.width(124),
            ),
          )
        ],
      ),
    );
  }
}
