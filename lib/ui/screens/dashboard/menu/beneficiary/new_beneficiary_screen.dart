import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class NewBeneficiaryScreen extends StatefulWidget {
  const NewBeneficiaryScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  State<NewBeneficiaryScreen> createState() => _NewBeneficiaryScreenState();
}

class _NewBeneficiaryScreenState extends State<NewBeneficiaryScreen> {
  final FocusNode accountNumFocus = FocusNode();
  final FocusNode fNameFocusNode = FocusNode();
  final FocusNode lNameFocusNode = FocusNode();
  final FocusNode emailFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var bankVM = context.read<BankVM>();
      bankVM.getBanksByCurrencyId(widget.currency.id ?? 0);
    });
  }

  @override
  void dispose() {
    accountNumFocus.dispose();
    fNameFocusNode.dispose();
    lNameFocusNode.dispose();
    emailFocusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BeneficiaryVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                children: [
                  CustomHeader(
                    showHeader: true,
                    headerText: 'New Beneficiary',
                    onBackBtnTap: () {
                      vm.clearData();
                      context.read<BankVM>().resetData();
                      Navigator.pop(context);
                    },
                  ),
                  const YBox(40),
                  if (widget.currency.code == CurrencyConstant.cadCurrency)
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: CustomTextField(
                                focusNode: fNameFocusNode,
                                labelText: "First Name",
                                showLabelHeader: true,
                                borderRadius: Sizer.height(4),
                                // hintText: '**********',
                                controller: vm.fNameC,
                                onChanged: (val) => vm.reBuildUI(),
                                onSubmitted: (p0) {
                                  fNameFocusNode.unfocus();
                                  FocusScope.of(context)
                                      .requestFocus(emailFocusNode);
                                },
                              ),
                            ),
                            const XBox(16),
                            Expanded(
                              child: CustomTextField(
                                focusNode: lNameFocusNode,
                                labelText: "Last Name",
                                showLabelHeader: true,
                                borderRadius: Sizer.height(4),
                                // hintText: '**********',
                                controller: vm.lNameC,
                                onChanged: (val) => vm.reBuildUI(),
                                onSubmitted: (p0) {
                                  lNameFocusNode.unfocus();
                                  FocusScope.of(context)
                                      .requestFocus(emailFocusNode);
                                },
                              ),
                            ),
                          ],
                        ),
                        const YBox(24),
                        CustomTextField(
                          focusNode: emailFocusNode,
                          labelText: "Interac E-mail",
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          errorText:
                              vm.emailC.text.isNotEmpty && !vm.isValidEmail
                                  ? "Invalid Email"
                                  : null,
                          // hintText: '**********',
                          controller: vm.emailC,
                          onChanged: (val) => vm.emailIsValid(),
                          onSubmitted: (p0) {
                            emailFocusNode.unfocus();
                          },
                        ),
                      ],
                    ),
                  if (widget.currency.code == CurrencyConstant.nairaCurrency)
                    Column(
                      children: [
                        CustomTextField(
                          labelText: "Bank",
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: 'Select Bank',
                          isReadOnly: true,
                          onTap: () {
                            BsWrapper.bottomSheet(
                                context: context,
                                widget: BankSheet(
                                  recipientCurrencyId: widget.currency.id,
                                ));
                          },
                          suffixIcon: Icon(
                            Icons.expand_more,
                            size: Sizer.radius(25),
                            color: AppColors.gray500,
                          ),
                          controller: vm.bankNameC,
                          onChanged: (val) {},
                        ),
                        const YBox(24),
                        CustomTextField(
                          focusNode: accountNumFocus,
                          labelText: "Account Number",
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: '**********',
                          keyboardType: KeyboardType.number,
                          controller: vm.accountNumC,
                          onChanged: (val) {
                            if (val.trim().length == 10) {
                              accountNumFocus.unfocus();
                              vm
                                  .verifyBankAcct(
                                bankId: vm.bankUUID ?? '',
                                accountNum: vm.accountNumC.text,
                                amount: "100",
                              )
                                  .then((value) {
                                if (!value.success) {
                                  FlushBarToast.fLSnackBar(
                                    message: value.message ??
                                        'Unable to verify account, kindly try again',
                                  );
                                }
                              });
                            }
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Account Name",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          // hintText: 'RONE-ORUGBOH AJORITSEDERE',
                          hideBorder: true,
                          controller: vm.accountNameC,
                          onChanged: (val) => vm.reBuildUI(),
                        ),
                      ],
                    ),
                  const Spacer(),
                  CustomBtn.solid(
                    onTap: () {
                      _saveBeneficiary();
                      FocusScope.of(context).unfocus();
                    },
                    online: widget.currency.code == "CAD"
                        ? vm.cadButtonIsActive
                        : vm.ngnButtonIsActive,
                    text: "Save Beneficiary",
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _saveBeneficiary() {
    var beneficiaryVM = context.read<BeneficiaryVM>();
    PostBeneficiaryArgs arg = widget.currency.code == "CAD"
        ? PostBeneficiaryArgs(
            currencyId: widget.currency.id!,
            firstName: beneficiaryVM.fNameC.text.trim(),
            lastName: beneficiaryVM.lNameC.text.trim(),
            accountIdentifier: beneficiaryVM.emailC.text.trim(),
          )
        : PostBeneficiaryArgs(
            currencyId: widget.currency.id!,
            institutionName: beneficiaryVM.bankNameC.text.trim(),
            accountName: beneficiaryVM.accountNameC.text.trim(),
            accountIdentifier: beneficiaryVM.accountNumC.text.trim(),
            institutionCode: beneficiaryVM.bankUUID,
          );
    printty(arg.toString(), level: 'arg');
    beneficiaryVM
        .saveBeneficiary(
      arg: arg,
      transferMethodType: widget.currency.code == "CAD"
          ? TransferMethodType.interac
          : TransferMethodType.bankTransfer,
    )
        .then((value) {
      if (value.success) {
        context.read<BankVM>().resetData();
        beneficiaryVM
          ..clearData()
          ..getBeneficiaries();
        _showComfirmationScreen(msg: value.message.toString());
      } else {
        _showComfirmationScreen(
          isFailed: true,
          msg: value.message.toString(),
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Beneficiary Saved\n Failed"
                : "Beneficiary Saved\n Successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
          if (!isFailed) {
            _pop();
            _pop();
            _pop();
            Navigator.pushNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.beneficiaryScreen,
            );
          }
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
