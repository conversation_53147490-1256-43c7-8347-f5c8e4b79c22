import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:lottie/lottie.dart';

class SuccessConfirmArg {
  SuccessConfirmArg({
    required this.btnText,
    this.imgPath,
    this.secondBtnText,
    required this.title,
    this.isInteracTransaction = false,
    this.interacSecurityAnswer,
    this.subTitle,
    this.btnTap,
    this.secondBtnTap,
  });

  final String? imgPath;
  final String btnText;
  final String? secondBtnText;
  final String title;
  final String? subTitle;
  final String? interacSecurityAnswer;
  final bool isInteracTransaction;
  final VoidCallback? btnTap;
  final VoidCallback? secondBtnTap;
}

class SuccessConfirmScreen extends StatelessWidget {
  const SuccessConfirmScreen({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final SuccessConfirmArg arg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              Container(
                alignment: Alignment.bottomLeft,
                child: ArrowBack(
                  onTap: arg.btnTap,
                ),
              ),
              const YBox(50),
              Lottie.asset(
                arg.imgPath ?? AppGifs.successLottie,
                height: Sizer.height(180),
              ),
              // imageHelper(
              //   arg.imgPath ?? AppImages.success,
              //   height: Sizer.height(160),
              //   // width: Sizer.width(159),
              // ),
              const YBox(82),
              Text(
                arg.title,
                textAlign: TextAlign.center,
                style: AppTypography.text20.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(8),
              if (arg.subTitle != null)
                arg.isInteracTransaction
                    ? const InteracRichText()
                    : Text(
                        arg.subTitle ?? "",
                        textAlign: TextAlign.center,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.textBlack800,
                        ),
                      ),
              const YBox(50),
              if (arg.isInteracTransaction &&
                  arg.interacSecurityAnswer?.isNotEmpty == true)
                CustomTextField(
                  labelText: 'Interac Security Answer',
                  showLabelHeader: true,
                  borderRadius: Sizer.height(4),
                  hintText: arg.interacSecurityAnswer ?? "",
                  hintStyle: AppTypography.text14,
                  isReadOnly: true,
                  suffixIcon: CopyWithIcon(
                    margin: EdgeInsets.only(
                      top: Sizer.height(11),
                      bottom: Sizer.height(11),
                      right: Sizer.width(16),
                    ),
                    onPressed: () {
                      Clipboard.setData(
                        ClipboardData(
                          text: arg.interacSecurityAnswer ?? "",
                        ),
                      );
                      FlushBarToast.fLSnackBar(
                        message: "Copied to clipboard",
                        snackBarType: SnackBarType.success,
                      );
                    },
                  ),
                ),
              const Spacer(),
              CustomBtn.solid(
                onTap: arg.btnTap,
                online: true,
                text: arg.btnText,
              ),
              const YBox(16),
              if (arg.secondBtnTap != null)
                CustomBtn.solid(
                  height: Sizer.height(56),
                  isOutline: true,
                  textColor: AppColors.primaryBlue,
                  onTap: arg.secondBtnTap,
                  online: true,
                  text: arg.secondBtnText ?? "Cancel",
                ),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}

// Message for Interac transaction
class InteracRichText extends StatelessWidget {
  const InteracRichText({super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          TextSpan(
            text:
                "Copy the interac security answer below to complete your transaction.",
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          // TextSpan(
          //   text: "phone",
          //   style: AppTypography.text16.copyWith(
          //     fontWeight: FontWeight.bold,
          //     color: AppColors.textBlack800,
          //   ),
          // ),
        ],
      ),
    );
  }
}
