import 'package:korrency/core/core.dart';
import 'package:provider/single_child_widget.dart';

final allProviders = <SingleChildWidget>[
  ChangeNotifierProvider(create: (_) => BaseVM()),
  ChangeNotifierProvider(create: (_) => ConfigVM()),
  ChangeNotifierProvider(create: (_) => DashboardVM()),
  ChangeNotifierProvider(create: (_) => InactivityVM()),
  ChangeNotifierProvider(create: (_) => OnBoardVM()),
  ChangeNotifierProvider(create: (_) => LoginVM()),
  ChangeNotifierProvider(create: (_) => AuthUserVM()),
  ChangeNotifierProvider(create: (_) => PasskeyResetVM()),
  ChangeNotifierProvider(create: (_) => KycVM()),
  ChangeNotifierProvider(create: (_) => WalletVM()),
  ChangeNotifierProvider(create: (_) => SendMoneyVM()),
  ChangeNotifierProvider(create: (_) => ConvertMoneyVM()),
  ChangeNotifierProvider(create: (_) => NotificationVM()),
  ChangeNotifierProvider(create: (_) => CurrencyVM()),
  ChangeNotifierProvider(create: (_) => TransactionVM()),
  ChangeNotifierProvider(create: (_) => ReferralVM()),
  ChangeNotifierProvider(create: (_) => BeneficiaryVM()),
  ChangeNotifierProvider(create: (_) => BankVM()),
  ChangeNotifierProvider(create: (_) => TrustedDeviceVM()),
  ChangeNotifierProvider(create: (_) => SecQuestVM()),
  ChangeNotifierProvider(create: (_) => AddressSuggestionVM()),
  ChangeNotifierProvider(create: (_) => TransactionPinVM()),
  ChangeNotifierProvider(create: (_) => CreateOfferVM()),
  ChangeNotifierProvider(create: (_) => MarketRateAlertVM()),
  ChangeNotifierProvider(create: (_) => MyOfferVM()),
  ChangeNotifierProvider(create: (_) => MarketPlaceOfferVM()),
  ChangeNotifierProvider(create: (_) => PhoneBookVM()),
  ChangeNotifierProvider(create: (_) => FreshChatVM()),
  ChangeNotifierProvider(create: (_) => InteracVM()),
  ChangeNotifierProvider(create: (_) => EmailPhoneChangeVM()),
];
