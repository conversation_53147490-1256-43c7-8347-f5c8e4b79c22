import 'package:card_swiper/card_swiper.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  int currentIndex = 0;

  List titles = [
    {"first": "Exchange ", "last": "Easily"},
    {"first": "Transfer ", "last": "Globally"},
    {"first": "Multi-currency ", "last": "Accounts"},
    {"first": "24/7 ", "last": "Support"},
  ];

  List subTitles = [
    "Securely exchange money with your peers at your own rate for your complete peace of mind",
    "Send and receive money internationally at competitive exchange rates, Guaranteed!",
    "Manage multiple currencies seamlessly in one place for your exchanges and global transfers",
    "Always available to assist you, anytime, anywhere",
  ];

  List<String> img = [
    AppImages.onboard0,
    AppImages.onboard2,
    AppImages.onboard1,
    AppImages.onboard3,
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        left: false,
        right: false,
        bottom: false,
        top: false,
        child: SizedBox(
          width: Sizer.screenWidth,
          height: Sizer.screenHeight,
          child: Column(
            children: [
              const YBox(20),
              Expanded(
                child: Swiper(
                  autoplay: true,
                  autoplayDisableOnInteraction: true,
                  autoplayDelay: 5000,
                  duration: 1000,
                  itemBuilder: (BuildContext context, int index) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const YBox(120),
                        SizedBox(
                          height: 300,
                          child: imageHelper(img[index]),
                        ),
                      ],
                    );
                  },
                  itemCount: 4,
                  onIndexChanged: (index) {
                    onIndexChanged(index);
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: Column(
                  children: [
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: titles[currentIndex]["first"],
                            style: AppTypography.text20.copyWith(
                              color: AppColors.black900,
                              fontFamily: "Inter",
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          TextSpan(
                            text: titles[currentIndex]["last"],
                            style: AppTypography.text20.copyWith(
                              color: AppColors.primaryBlue,
                              fontFamily: "Inter",
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const YBox(8),
                    Text(
                      subTitles[currentIndex],
                      textAlign: TextAlign.center,
                      style: AppTypography.text16.copyWith(
                        color: AppColors.textBlack800,
                      ),
                    ),
                    const YBox(35),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        SwipeIndicator(
                          isActive: currentIndex == 0,
                        ),
                        const XBox(8),
                        SwipeIndicator(
                          isActive: currentIndex == 1,
                        ),
                        const XBox(8),
                        SwipeIndicator(
                          isActive: currentIndex == 2,
                        ),
                        const XBox(8),
                        SwipeIndicator(
                          isActive: currentIndex == 3,
                        )
                      ],
                    ),
                    const YBox(30),
                    CustomBtn.solid(
                      height: Sizer.height(65),
                      onTap: () {
                        Navigator.of(context)
                            .pushNamed(RoutePath.createAcctScreen);
                      },
                      online: true,
                      text: "Get Started",
                    ),
                    const YBox(16),
                    CustomBtn.solid(
                      height: Sizer.height(56),
                      isOutline: true,
                      textColor: AppColors.primaryBlue,
                      onTap: () {
                        Navigator.of(context).pushNamed(RoutePath.loginScreen);
                      },
                      online: true,
                      text: "Sign in",
                    ),
                    const YBox(50),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  onIndexChanged(int index) {
    if (currentIndex <= 3) {
      setState(() {
        currentIndex = index;
      });
    } else {
      currentIndex = 0;
    }
  }
}
