import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateErrorModal extends StatefulWidget {
  const RateErrorModal({super.key});

  @override
  State<RateErrorModal> createState() => _RateErrorModalState();
}

class _RateErrorModalState extends State<RateErrorModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(80),
                Text(
                  'Couldn\'t send feedback. \nPlease try again',
                  textAlign: TextAlign.center,
                  style: FontTypography.text20.semiBold,
                ),
                const YBox(28),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {},
                  text: "Try Again",
                ),
                const YBox(14),
                Text(
                  'Maybe Later',
                  textAlign: TextAlign.center,
                  style: FontTypography.text14.medium.withCustomColor(
                    AppColors.primaryBlue,
                  ),
                ),
                const YBox(36),
              ],
            ),
          ),
          Positioned(
            top: -Sizer.height(62),
            right: 0,
            left: 0,
            child: imageHelper(
              AppImages.ratingCancel,
              height: Sizer.height(124),
              width: Sizer.width(124),
            ),
          )
        ],
      ),
    );
  }
}
