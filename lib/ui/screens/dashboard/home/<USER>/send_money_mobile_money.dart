import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyMobileMoney extends StatefulWidget {
  const SendMoneyMobileMoney({
    super.key,
    required this.paymentMethod,
  });

  final PaymentMethod paymentMethod;

  @override
  State<SendMoneyMobileMoney> createState() => _SendMoneyMobileMoneyState();
}

class _SendMoneyMobileMoneyState extends State<SendMoneyMobileMoney> {
  final operatorC = TextEditingController();
  final mobileNumberC = TextEditingController();
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();
  final operatorF = FocusNode();
  final mobileNumberF = FocusNode();
  final recipientFirstNameF = FocusNode();
  final recipientLastNameF = FocusNode();

  bool useBeneficiary =
      false; // flag to check if the user wants to use a beneficiary
  bool hasStartedVerification = false;
  bool isMobileNumberValid = false;
  bool mobileNumberHasBeenTapped = false;
  MobileMoney? selectedMobileMoney;

  int selectedBeneficiaryIndex = -1; // -1 means no beneficiary seleccted

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final sendMoneyVM = context.read<SendMoneyVM>();
      final bankVm = context.read<BankVM>();
      final beneVm = context.read<BeneficiaryVM>();

      beneVm.getBeneficiaries(
        currencyId: sendMoneyVM.recipientCurrency?.id,
        transferMethod: widget.paymentMethod.id,
      );

      bankVm.getMNO(sendMoneyVM.recipientCurrency?.id ?? 0);
    });
  }

  @override
  void dispose() {
    operatorC.dispose();
    mobileNumberC.dispose();
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();

    operatorF.dispose();
    mobileNumberF.dispose();
    recipientFirstNameF.dispose();
    recipientLastNameF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                const YBox(20),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: CustomHeader(
                    showBackBtn: true,
                    showHeader: true,
                    onBackBtnTap: () {
                      vm.setMobileMoney(null);
                      Navigator.pop(context);
                    },
                    headerText:
                        'Send to ${vm.recipientCurrency?.code} Mobile Money',
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const YBox(24),
                              CustomTextField(
                                controller: operatorC,
                                focusNode: operatorF,
                                labelText: "Select Operator",
                                showLabelHeader: true,
                                isReadOnly: true,
                                hintText: 'Select Operator',
                                borderRadius: Sizer.height(4),
                                showSuffixIcon: true,
                                suffixIcon: Icon(
                                  Icons.expand_more,
                                  size: Sizer.radius(25),
                                ),
                                onTap: () async {
                                  final res = await BsWrapper.bottomSheet(
                                    context: context,
                                    widget: MobileMoneySheet(
                                        title: operatorC.text.trim()),
                                  );
                                  if (res is MobileMoney) {
                                    operatorC.text = res.name ?? '';
                                    selectedMobileMoney = res;
                                    if (mobileNumberC.text.length > 9 &&
                                        widget.paymentMethod.nameCheck ==
                                            true) {
                                      verifyMobileWalletByCurrecyId();
                                    }
                                    useBeneficiary = false;
                                    selectedBeneficiaryIndex = -1;
                                    setState(() {});
                                  }
                                },
                              ),
                              const YBox(24),
                              CustomTextField(
                                focusNode: mobileNumberF,
                                controller: mobileNumberC,
                                labelText: "Mobile Number",
                                showLabelHeader: true,
                                isReadOnly: operatorC.text.trim().isEmpty,
                                keyboardType: KeyboardType.number,
                                hintText: '**********',
                                borderRadius: Sizer.height(4),
                                errorText: mobileNumberHasBeenTapped &&
                                        operatorC.text.trim().isEmpty
                                    ? 'Please select operator'
                                    : null,
                                suffixIcon: vm.busy(verifyingBankState)
                                    ? const CupertinoActivityIndicator()
                                    : null,
                                onChanged: (val) {
                                  hasStartedVerification = false;
                                  useBeneficiary = false;
                                  selectedBeneficiaryIndex = -1;
                                  if (val.trim().length > 9 &&
                                      widget.paymentMethod.nameCheck == true) {
                                    verifyMobileWalletByCurrecyId();
                                  }
                                  setState(() {});
                                },
                                onTap: () {
                                  if (operatorC.text.trim().isEmpty) {
                                    mobileNumberHasBeenTapped = true;
                                    setState(() {});
                                  }
                                },
                              ),
                              const YBox(4),
                              if (hasStartedVerification &&
                                  widget.paymentMethod.nameCheck == true)
                                Row(
                                  children: [
                                    Text(
                                      isMobileNumberValid
                                          ? 'Active'
                                          : 'Not active',
                                      style: AppTypography.text12.copyWith(
                                        color: isMobileNumberValid
                                            ? AppColors.baseGreen
                                            : AppColors.red,
                                      ),
                                    ),
                                    const XBox(4),
                                    Icon(
                                      isMobileNumberValid
                                          ? Icons.check_circle
                                          : Icons.error,
                                      size: Sizer.radius(13),
                                      color: isMobileNumberValid
                                          ? AppColors.baseGreen
                                          : AppColors.red,
                                    ),
                                  ],
                                ),
                              const YBox(20),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: CustomTextField(
                                        focusNode: recipientFirstNameF,
                                        controller: recipientFirstNameC,
                                        labelText: "Recipient",
                                        showLabelHeader: true,
                                        hintText: 'First Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) {
                                          useBeneficiary = false;
                                          selectedBeneficiaryIndex = -1;
                                          setState(() {});
                                        }),
                                  ),
                                  const XBox(10),
                                  Expanded(
                                    child: CustomTextField(
                                        focusNode: recipientLastNameF,
                                        controller: recipientLastNameC,
                                        hintText: 'Last Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) {
                                          useBeneficiary = false;
                                          selectedBeneficiaryIndex = -1;
                                          setState(() {});
                                        }),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const YBox(70),
                        Consumer<BeneficiaryVM>(builder: (context, beneVM, _) {
                          if (beneVM.beneficiariesByCurrencyId.isEmpty) {
                            return const YBox(160);
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(24),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Saved Beneficiaries",
                                      style: AppTypography.text14.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        Navigator.pushNamed(
                                          context,
                                          RoutePath.beneficiaryScreen,
                                          arguments: BeneficiaryArg(
                                            currencyId:
                                                vm.recipientCurrency?.id ?? 0,
                                          ),
                                        );
                                      },
                                      child: Text(
                                        "View all",
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.gray500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const YBox(24),
                              Builder(builder: (context) {
                                if (context.read<BeneficiaryVM>().isBusy) {
                                  return SizedBox(
                                    height: Sizer.height(80),
                                    child: const Center(
                                      child: CupertinoActivityIndicator(),
                                    ),
                                  );
                                }
                                return SizedBox(
                                  height: Sizer.height(120),
                                  child: ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: Sizer.width(20)),
                                    itemBuilder: (ctx, i) {
                                      var beneficiary =
                                          beneVM.beneficiariesByCurrencyId[i];
                                      return BeneficiaryCard(
                                        title: beneficiary.accountName ?? "",
                                        imgPath: beneficiary.iconUrl ?? "",
                                        isSelected:
                                            selectedBeneficiaryIndex == i,
                                        onTap: () {
                                          selectedBeneficiaryIndex = i;

                                          _setFirstAndLastNameFromBeneficiary(
                                              beneficiary);
                                          // if (widget.paymentMethod.nameCheck ==
                                          //     false) {

                                          // }
                                        },
                                      );
                                    },
                                    separatorBuilder: (ctx, _) =>
                                        const XBox(24),
                                    itemCount: beneVM.beneficiariesByCurrencyId
                                        .take(10)
                                        .length,
                                  ),
                                );
                              }),
                            ],
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomSheet: Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.solid(
              onTap: () async {
                if (!useBeneficiary) {
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    vm.setMobileMoney(MobileMoney(
                      uuid: selectedMobileMoney?.uuid ?? '',
                      name: selectedMobileMoney?.name ?? '',
                      mobileNumber: mobileNumberC.text.trim(),
                      recipientName:
                          '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}',
                    ));

                    Navigator.pushNamed(
                      context,
                      RoutePath.reviewScreen,
                    );
                  }

                  return;
                }
                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                );
              },
              online: recipientFirstNameC.text.trim().isNotEmpty &&
                  recipientLastNameC.text.trim().isNotEmpty &&
                  operatorC.text.trim().isNotEmpty &&
                  mobileNumberC.text.trim().isNotEmpty,
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }

  _setFirstAndLastNameFromBeneficiary(Beneficiary beneficiary) {
    final vm = context.read<SendMoneyVM>();
    operatorC.text = beneficiary.institutionName ?? "";
    mobileNumberC.text = beneficiary.accountIdentifier ?? "";
    recipientFirstNameC.text = beneficiary.firstName ?? "";
    recipientLastNameC.text = beneficiary.lastName ?? "";

    vm.setMobileMoney(MobileMoney(
      uuid: beneficiary.institutionCode ?? '',
      name: beneficiary.institutionName ?? '',
      mobileNumber: beneficiary.accountIdentifier ?? '',
      recipientName: beneficiary.accountName ?? '',
    ));

    useBeneficiary = true;

    setState(() {});
  }

  void verifyMobileWalletByCurrecyId() {
    final vm = context.read<SendMoneyVM>();
    vm
        .verifyMobileWalletByCurrecyId(
      currencyId: vm.recipientCurrency?.id ?? 0,
      destinationNumber: mobileNumberC.text,
    )
        .then((v) {
      hasStartedVerification = true;
      isMobileNumberValid = widget.paymentMethod.nameCheck == true
          ? (v.data ?? '').toString().toLowerCase() == 'active'
          : true;
    });
  }
}
