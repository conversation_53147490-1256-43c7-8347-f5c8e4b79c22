import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendToCadScreen extends StatefulWidget {
  const SendToCadScreen({super.key});

  @override
  State<SendToCadScreen> createState() => _SendToCadScreenState();
}

class _SendToCadScreenState extends State<SendToCadScreen> {
  int _selectedIndex = -1;

  final _fNameFocusNode = FocusNode();
  final _lNameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BeneficiaryVM>().getBeneficiaries(
            currencyId: context.read<SendMoneyVM>().recipientCurrency?.id,
          );
    });
  }

  @override
  void dispose() {
    _fNameFocusNode.dispose();
    _lNameFocusNode.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                const YBox(20),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  child: const CustomHeader(
                    // showBackBtn: true,
                    showHeader: true,
                    headerText: 'Send to CAD Bank Account',
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const YBox(50),
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                          child: Row(
                            children: [
                              Expanded(
                                child: CustomTextField(
                                  focusNode: _fNameFocusNode,
                                  labelText: "First Name",
                                  showLabelHeader: true,
                                  borderRadius: Sizer.height(4),
                                  controller: vm.interacFNameC,
                                  onChanged: (val) {
                                    _selectedIndex = -1;
                                    vm.reBuildUI();
                                  },
                                  onSubmitted: (p0) {
                                    _fNameFocusNode.unfocus();
                                    FocusScope.of(context)
                                        .requestFocus(_emailFocusNode);
                                  },
                                ),
                              ),
                              const XBox(16),
                              Expanded(
                                child: CustomTextField(
                                  focusNode: _lNameFocusNode,
                                  labelText: "Last Name",
                                  showLabelHeader: true,
                                  borderRadius: Sizer.height(4),
                                  controller: vm.interacLNameC,
                                  onChanged: (val) {
                                    _selectedIndex = -1;
                                    vm.reBuildUI();
                                  },
                                  onSubmitted: (p0) {
                                    _lNameFocusNode.unfocus();
                                    FocusScope.of(context)
                                        .requestFocus(_emailFocusNode);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        const YBox(24),
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                          child: CustomTextField(
                            focusNode: _emailFocusNode,
                            labelText: "Interac E-mail",
                            showLabelHeader: true,
                            borderRadius: Sizer.height(4),
                            controller: vm.interacEmailC,
                            errorText: _errortext(),
                            onChanged: (val) {
                              _selectedIndex = -1;
                              vm.reBuildUI();
                            },
                            onSubmitted: (p0) {
                              _emailFocusNode.unfocus();
                            },
                          ),
                        ),
                        const YBox(70),
                        Consumer<BeneficiaryVM>(builder: (context, beneVM, _) {
                          if (beneVM.beneficiariesByCurrencyId.isEmpty) {
                            return const YBox(160);
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(24),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Saved Beneficiaries",
                                      style: AppTypography.text14.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        Navigator.pushNamed(
                                          context,
                                          RoutePath.beneficiaryScreen,
                                          arguments: BeneficiaryArg(
                                            currencyId:
                                                vm.recipientCurrency?.id ?? 0,
                                            transferMethodType:
                                                TransferMethodType.interac,
                                          ),
                                        );
                                      },
                                      child: Text(
                                        "View all",
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.gray500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const YBox(24),
                              Builder(builder: (context) {
                                if (context.read<BeneficiaryVM>().isBusy) {
                                  return SizedBox(
                                    height: Sizer.height(80),
                                    child: const Center(
                                      child: CupertinoActivityIndicator(),
                                    ),
                                  );
                                }
                                return SizedBox(
                                  height: Sizer.height(120),
                                  child: ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: Sizer.width(20)),
                                    itemBuilder: (ctx, i) {
                                      var beneficiary =
                                          beneVM.beneficiariesByCurrencyId[i];
                                      return BeneficiaryCard(
                                        title: beneficiary.accountName ?? "",
                                        imgPath: beneficiary.iconUrl ?? "",
                                        isSelected: _selectedIndex == i,
                                        onTap: () {
                                          _selectedIndex = i;
                                          vm.autoFillInteracEmailName(
                                              beneficiary);
                                        },
                                      );
                                    },
                                    separatorBuilder: (ctx, _) =>
                                        const XBox(24),
                                    itemCount: beneVM.beneficiariesByCurrencyId
                                        .take(10)
                                        .length,
                                  ),
                                );
                              }),
                            ],
                          );
                        }),
                        const YBox(150),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomSheet: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(20),
            ),
            child: CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();

                if (_selectedIndex == -1) {
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    Navigator.pushNamed(
                      context,
                      RoutePath.reviewScreen,
                    );
                  }

                  return;
                }

                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                );
              },
              online: vm.cadButtonIsActive && !_checkDepositInterac(),
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }

  String? _errortext() {
    final sendVm = context.read<SendMoneyVM>();
    if (sendVm.interacEmailC.text.isNotEmpty && !sendVm.isInteracEmaiLValid) {
      return 'Invalid email';
    } else if (sendVm.interacEmailC.text.isNotEmpty &&
        (!sendVm.isInteracEmaiLValid || _checkDepositInterac())) {
      return 'You cannot use the inputted interac email as a recipient';
    } else {
      return null;
    }
  }

  bool _checkDepositInterac() {
    return context.read<SendMoneyVM>().interacEmailC.text.trim() ==
        context.read<ConfigVM>().interacDepositEmail;
  }
}
