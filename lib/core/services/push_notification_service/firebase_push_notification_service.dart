import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:korrency/core/core.dart';

class FirebasePushNotificationService {
  static final _firebaseMessaging = FirebaseMessaging.instance;

  static Future init(
    Future<void> Function(RemoteMessage) backgroundHandler,
  ) async {
    printty("===> fcm initializing......");

    // Check for Samsung device to apply special handling
    bool isSamsungDevice = false;
    if (Platform.isAndroid) {
      try {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        isSamsungDevice = androidInfo.manufacturer.toLowerCase() == 'samsung';
        if (isSamsungDevice) {
          printty(
              "Samsung device detected, applying special permission handling");
        }
      } catch (e) {
        printty("Error detecting device info: $e");
      }
    }

    // Get current permission status
    NotificationSettings currentSettings =
        await _firebaseMessaging.getNotificationSettings();
    printty(
        "Current notification permission status: ${currentSettings.authorizationStatus}");

    // Request permissions with proper error handling
    try {
      // For Samsung devices, ensure UI is ready
      if (isSamsungDevice) {
        await Future.delayed(const Duration(milliseconds: 400));
      }

      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: false,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );

      // Process permission result
      printty('User granted permission: ${settings.authorizationStatus}');

      // For Samsung devices, ensure UI updates after permission request
      if (isSamsungDevice) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          printty("Samsung device: Frame built after permission request");
        });
      }
    } catch (e) {
      printty("FCM permission request error: $e");
    }

    // Listen to background notifications
    FirebaseMessaging.onBackgroundMessage(backgroundHandler);

    // to handle foreground notifications
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      printty('Got a message whilst in the foreground!');
      printty('Message title: ${message.notification?.title.toString()}');
      printty('Message body: ${message.notification?.body.toString()}');
      printty('Message data: ${message.data.toString()}');

      _handleMessage(message);
    });

    // Get any messages which caused the application to open from
    // a terminated state.
    try {
      RemoteMessage? initialMessage =
          await _firebaseMessaging.getInitialMessage();

      // If the message also contains a data property with a "type" of "chat",
      // navigate to a chat screen
      if (initialMessage != null) {
        _handleMessage(initialMessage);
      }
    } catch (e) {
      printty("Error getting initial message: $e");
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);

    // Get token and set up token refresh listener
    try {
      // Initialize FCM token service
      final fcmTokenService = FCMTokenService();

      // Get initial token
      String? token = await fcmTokenService.getToken();
      printty("Initial FCM Token: $token");

      // Set up token refresh listener
      fcmTokenService.setupTokenRefreshListener();

      // For Android, ensure token is valid by requesting it again after a delay
      if (Platform.isAndroid) {
        // This helps with some Android devices that might have issues with initial token generation
        Future.delayed(const Duration(seconds: 5), () async {
          String? refreshedToken = await getFcmToken();
          if (refreshedToken != token) {
            printty("FCM Token refreshed after delay: $refreshedToken");
            await StorageService.storeStringItem(
                StorageKey.deviceToken, refreshedToken ?? "");
          }
        });
      }
    } catch (e) {
      printty("Error setting up FCM token: $e");
    }
  }

  static Future<void> firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    printty('Handling a background message ${message.messageId}');
    _handleMessage(message);
  }

  static Future<String?> getFcmToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      printty("Error getting FCM token: $e");
      return null;
    }
  }

  static Future<String?> getAPNToken() async {
    try {
      return await _firebaseMessaging.getAPNSToken();
    } catch (e) {
      printty("Error getting APNS token: $e");
      return null;
    }
  }

  static Future<void> subscribeAndUnsubscribeToTopic(
      {required String topic, required bool subscribe}) async {
    try {
      subscribe
          ? await _firebaseMessaging.subscribeToTopic(topic)
          : await _firebaseMessaging.unsubscribeFromTopic(topic);
      printty(
          "${subscribe ? 'Subscribed to' : 'Unsubscribed from'} topic: $topic");
    } catch (e) {
      printty(
          "Error ${subscribe ? 'subscribing to' : 'unsubscribing from'} topic $topic: $e");
    }
  }

  static void _handleMessage(RemoteMessage message) {
    try {
      LocalPushNotificationService.showNotification(
          title: message.notification?.title ?? "Korrency",
          body: message.notification?.body ??
              "You have a notification from Korrency",
          payload: json.encode(message.data));
    } catch (e) {
      printty("Error handling message: $e");
    }
  }
}

// class FirebasePushNotificationService {
//   static final _firebaseMessaging = FirebaseMessaging.instance;
//   static Future init(
//     Future<void> Function(RemoteMessage) backgroundHandler,
//   ) async {
//     printty("===> fcm initializing......");

//     await _firebaseMessaging.requestPermission(
//       alert: true,
//       announcement: true,
//       badge: true,
//       carPlay: false,
//       criticalAlert: true,
//       provisional: false,
//       sound: true,
//     );

//     // Listen to background notifications
//     FirebaseMessaging.onBackgroundMessage(backgroundHandler);

//     // to handle foreground notifications
//     FirebaseMessaging.onMessage.listen((RemoteMessage message) {
//       printty('Got a message whilst in the foreground!');
//       printty('Message title: ${message.notification?.title.toString()}');
//       printty('Message body: ${message.notification?.body.toString()}');
//       printty('Message data: ${message.data.toString()}');

//       _handleMessage(message);
//     });

//     // Get any messages which caused the application to open from
//     // a terminated state.
//     RemoteMessage? initialMessage =
//         await _firebaseMessaging.getInitialMessage();

//     // If the message also contains a data property with a "type" of "chat",
//     // navigate to a chat screen
//     if (initialMessage != null) {
//       _handleMessage(initialMessage);
//     }

//     // Also handle any interaction when the app is in the background via a
//     // Stream listener
//     FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
//   }

//   static Future<void> firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     // print('Handling a background message ${message.messageId}');
//     _handleMessage(message);
//     //print('Handling a background message ${message.messageId}');
//   }

//   static Future<String?> getFcmToken() async {
//     return await _firebaseMessaging.getToken();
//   }

//   static Future<String?> getAPNToken() async {
//     return await _firebaseMessaging.getAPNSToken();
//   }

//   static subscribeAndUnsubscribeToTopic(
//       {required String topic, required bool subscribe}) async {
//     subscribe
//         ? await _firebaseMessaging.subscribeToTopic(topic)
//         : await _firebaseMessaging.unsubscribeFromTopic(topic);
//   }

//   static _handleMessage(RemoteMessage message) {
//     LocalPushNotificationService.showNotification(
//         title: message.notification?.title ?? "Korrency",
//         body: message.notification?.body ??
//             "You have a notification from Korrency",
//         payload: json.encode(message.data));
//   }
// }
