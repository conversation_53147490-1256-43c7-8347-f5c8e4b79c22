import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/env/env.dart';
import 'package:korrency/core/utils/printty.dart';

class EnvConfig {
  static Future<void> initialize(String flavor) async {
    // final flavor = const String.fromEnvironment('FLAVOR', defaultValue: 'prod');
    final envFile = '.env.$flavor';

    try {
      // Load the appropriate .env file based on flavor
      await dotenv.load(fileName: envFile);
      printty('Loaded $envFile');

      // Set environment
      EnvironmentConfig.environment = Environment.values.firstWhere(
        (e) => e.name == flavor,
        orElse: () => throw Exception('Unknown flavor: $flavor'),
      );
    } on Exception catch (e) {
      printty('Error initializing environment: $e');
      rethrow;
    }
  }

  // Environment variables
  static String get apiUrl => dotenv.env['API_URL'] ?? '';
  static String get apiKey => dotenv.env['API_KEY'] ?? '';
  // static String get testPROD => dotenv.env['TEST_PROD'] ?? '';
  static String get mixpanelToken => dotenv.env['MIXPANEL_PROJECT_TOKEN'] ?? '';
  static int get focusTimeout =>
      int.tryParse(dotenv.env['FOCUS_TIMEOUT'] ?? '0') ?? 0;
  static int get inactivityTimeout =>
      int.tryParse(dotenv.env['INACTIVITY_TIMEOUT'] ?? '0') ?? 0;
}

/// Sec Quest
/// Mother maiden name: Boss
/// City u were born: Benin
/// fav movie: Avatar



// flutterz build apk --flavor development --target lib/main_dev.dart
// flutterz build apk --flavor staging --target lib/main_staging.dart
// flutterz build apk --flavor production --target lib/main_prod.dart

// flutterz build appbundle --flavor development -t lib/main_dev.dart
// flutterz build appbundle --flavor staging -t lib/main_staging.dart
// flutterz build appbundle --flavor production -t lib/main_prod.dart