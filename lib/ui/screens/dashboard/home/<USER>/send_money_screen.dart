import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyArg {
  final String fromCurrencyCode;
  final String toCurrencyCode;

  SendMoneyArg({required this.fromCurrencyCode, required this.toCurrencyCode});
}

class SendMoneyScreen extends StatefulWidget {
  const SendMoneyScreen({super.key, this.arg});

  final SendMoneyArg? arg;

  @override
  State<SendMoneyScreen> createState() => _SendMoneyScreenState();
}

class _SendMoneyScreenState extends State<SendMoneyScreen> {
  final FocusNode _fromFocusNode = FocusNode();
  final FocusNode _recipientFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _fromFocusNode);
    _fromFocusNode.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
      _showNewCustomerOfferModal();
    });
  }

  _init() async {
    final walletVM = context.read<WalletVM>();
    final currencyVM = context.read<CurrencyVM>();
    final sendMoneyVM = context.read<SendMoneyVM>();
    final userVM = context.read<AuthUserVM>();
    final configVM = context.read<ConfigVM>();
    if (widget.arg != null) {
      await sendMoneyVM.init(
        cadWallet: _setWalletFromCurrency(widget.arg?.fromCurrencyCode ?? ''),
        recipientCurrency: _setCurrency(widget.arg?.toCurrencyCode ?? ''),
        walletList: walletVM.walletList,
        userHasTransaction: userVM.user?.hasTransactions,
        newCustomersRateMinimumAmount: configVM.newCustomersRateMinimumAmount,
      );
    } else {
      await sendMoneyVM.init(
        cadWallet: walletVM.cadWallet,
        recipientCurrency: currencyVM.nairaCurrency,
        walletList: walletVM.walletList,
        userHasTransaction: userVM.user?.hasTransactions,
        newCustomersRateMinimumAmount: configVM.newCustomersRateMinimumAmount,
      );
    }

    sendMoneyVM.getConversionRate();
    sendMoneyVM.getFees();
  }

  _setWalletFromCurrency(String code) {
    var walletVM = context.read<WalletVM>();
    Wallet walletFromCurrency = walletVM.walletList
        .firstWhere((element) => element.currency?.code == code);
    printty(walletFromCurrency, level: "walletFromCurrency");
    return walletFromCurrency;
  }

  _setCurrency(String code) {
    printty('_setCurrency called');
    var currencyVM = context.read<CurrencyVM>();
    Currency currency =
        currencyVM.currencies.firstWhere((element) => element.code == code);
    printty(currency, level: "walletToCurrency");
    return currency;
  }

  @override
  void dispose() {
    _fromFocusNode.dispose();
    _recipientFocusNode.dispose();
    super.dispose();
  }

  unfocusAllNode() {
    _fromFocusNode.unfocus();
    _recipientFocusNode.unfocus();
  }

  _showNewCustomerOfferModal() {
    final userVM = context.read<AuthUserVM>();
    if (userVM.user?.hasTransactions == false) {
      return BsWrapper.bottomSheet(
        context: context,
        widget: const NewCustomerOfferModal(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const YBox(20),
                  CustomHeader(
                    onBackBtnTap: () {
                      vm.resetData();
                      context.read<BankVM>().resetData();
                      context.read<TransactionVM>().setSelectedReason(null);
                      Navigator.pop(context);
                    },
                    showHeader: true,
                    headerText: 'Send Money',
                  ),
                  const YBox(26),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(14),
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.grayE9),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: SizedBox(
                      width: Sizer.screenWidth,
                      child: Column(
                        children: [
                          Container(
                            width: Sizer.width(202),
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(16),
                              vertical: Sizer.height(4),
                            ),
                            decoration: BoxDecoration(
                              color: vm.setRateBannerToGreen
                                  ? AppColors.transparent
                                  : AppColors.primaryBlue,
                              image: vm.setRateBannerToGreen
                                  ? const DecorationImage(
                                      image: AssetImage(
                                        AppImages.smGradient,
                                      ),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  vm.rateFormat,
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (vm.setRateBannerToGreen) const XBox(10),
                                if (vm.setRateBannerToGreen)
                                  Icon(
                                    Iconsax.information5,
                                    size: Sizer.radius(16),
                                    color: AppColors.white,
                                  ),
                              ],
                            ),
                          ),
                          const YBox(20),
                          Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'You send',
                                    style: AppTypography.text12.copyWith(
                                      color: AppColors.textGray,
                                    ),
                                  ),
                                  const YBox(4),
                                  Container(
                                    height: Sizer.height(30),
                                    color: AppColors.white,
                                    width: Sizer.width(120),
                                    child: ConvertTextfield(
                                      hintText: "10,000.00",
                                      textAlign: TextAlign.start,
                                      focusNode: _fromFocusNode,
                                      color: vm.amtSendIsGreaterThanBalance
                                          ? AppColors.red
                                          : AppColors.baseBlack,
                                      controller: vm.fromC,
                                      onChanged: (val) {
                                        printty(val, level: "val");

                                        // if (vm.fromConvertWallet?.currency?.code ==
                                        //     AppUtils.cadCurrency) {
                                        //   vm.setUserHasRateMinAmount(val.trim());
                                        // }

                                        vm
                                          ..setFromDecimalValue(
                                              val.trim().replaceAllCommas())
                                          ..setRecipientGetAmountToTextField()
                                          ..reBuildUI();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          'Balance: ${vm.fromConvertWalletBalance}',
                                          style: AppTypography.text11.copyWith(
                                            color: AppColors.textGray,
                                          ),
                                        ),
                                      ),
                                      const XBox(5),
                                      InkWell(
                                        onTap: () {
                                          vm.fromC.text =
                                              (vm.fromConvertWallet?.balance ??
                                                      "0")
                                                  .toString();
                                          vm
                                            ..setFromDecimalValue(
                                                vm.fromConvertWallet?.balance ??
                                                    "0")
                                            ..setRecipientGetAmountToTextField()
                                            ..reBuildUI();
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: Sizer.width(8),
                                            vertical: Sizer.height(4),
                                          ),
                                          decoration: const BoxDecoration(
                                            color: AppColors.primaryBlue,
                                            borderRadius: BorderRadius.all(
                                              Radius.circular(4),
                                            ),
                                          ),
                                          child: Text(
                                            'Add All',
                                            style:
                                                AppTypography.text10.copyWith(
                                              color: AppColors.white,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const YBox(4),
                                  CurrencyCards(
                                    isNetworkSvg:
                                        vm.fromConvertWallet?.currency?.flag !=
                                            null,
                                    currencyCode:
                                        vm.fromConvertWallet?.currency?.code ??
                                            "",
                                    showCurrencyCode: true,
                                    flagIconPath:
                                        vm.fromConvertWallet?.currency?.flag ??
                                            AppImages.ngn,
                                    onTap: () {
                                      BsWrapper.bottomSheet(
                                        context: context,
                                        widget: const SelectWalletCurrencySheet(
                                          fromConvert: true,
                                          isSendMoney: true,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                          const YBox(30),
                          Row(
                            children: [
                              const Expanded(
                                child: Divider(
                                  color: AppColors.primaryLightBlue,
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  vm.swapCurrency();
                                },
                                child: Container(
                                  padding: EdgeInsets.all(Sizer.radius(8)),
                                  decoration: BoxDecoration(
                                      color: AppColors.blue100,
                                      borderRadius: BorderRadius.circular(30),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: AppColors.grayE9,
                                          blurRadius: 4,
                                          offset: Offset(0, 4),
                                        ),
                                      ]),
                                  // alignment: Alignment.center,
                                  child: const Icon(Iconsax.arrow_swap),
                                ),
                              ),
                              const Expanded(
                                child: Divider(
                                  color: AppColors.grayE9,
                                ),
                              ),
                            ],
                          ),
                          const YBox(30),
                          Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  vm.recipientMinimumAmountCheck
                                      ? Text(
                                          'Minimum Amount: ${vm.recipientMinAmount}',
                                          style: AppTypography.text11.copyWith(
                                            color: AppColors.fail30,
                                          ),
                                        )
                                      : Text(
                                          'Recipient gets',
                                          style: AppTypography.text12.copyWith(
                                            color: AppColors.textGray,
                                          ),
                                        ),
                                  const YBox(4),
                                  SizedBox(
                                    height: Sizer.height(30),
                                    // color: AppColors.blue300,
                                    width: Sizer.width(150),
                                    child: ConvertTextfield(
                                      hintText: "10,000.00",
                                      textAlign: TextAlign.start,
                                      focusNode: _recipientFocusNode,
                                      color: vm.amtSendIsGreaterThanBalance ||
                                              vm.recipientMinimumAmountCheck
                                          ? AppColors.fail30
                                          : AppColors.baseBlack,
                                      controller: vm.recipientC,
                                      onChanged: (val) {
                                        printty(val, level: "val");
                                        // if (vm.recipientCurrency?.code ==
                                        //     AppUtils.cadCurrency) {
                                        //   vm.setUserHasRateMinAmount(val.trim());
                                        // }
                                        vm.senderGetAmount();
                                        vm.reBuildUI();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              CurrencyCards(
                                isNetworkSvg:
                                    vm.recipientCurrency?.flag != null,
                                currencyCode: vm.recipientCurrency?.code ?? "",
                                showCurrencyCode: true,
                                flagIconPath:
                                    vm.recipientCurrency?.flag ?? AppImages.ngn,
                                onTap: () {
                                  BsWrapper.bottomSheet(
                                    context: context, //
                                    widget: const SelectCurrencySheet(
                                      isSendMoney: true,
                                    ),
                                  );
                                },
                              )
                            ],
                          ),
                          const YBox(20),
                        ],
                      ),
                    ),
                  ),
                  const YBox(24),
                  Consumer<TransactionVM>(builder: (_, transactionVm, __) {
                    return CustomTextField(
                      labelText: "Reason for sending money *",
                      isReadOnly: true,
                      showLabelHeader: true,
                      borderRadius: Sizer.height(4),
                      hintText: transactionVm.selectedReason?.name ??
                          'Choose a suitable reason',
                      borderColor: AppColors.primaryLightBlue,
                      showSuffixIcon: true,
                      suffixIcon: Icon(
                        Iconsax.arrow_down_1,
                        color: AppColors.baseBlack,
                        size: Sizer.height(16),
                      ),
                      onTap: () {
                        BsWrapper.bottomSheet(
                          context: context, //
                          widget: const SendMoneyReasonSheet(),
                        );
                      },
                    );
                  }),
                  const YBox(32),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(12),
                      vertical: Sizer.height(8),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.blueFA,
                      border: Border.all(
                        color: AppColors.grayE9,
                      ),
                      borderRadius: BorderRadius.circular(Sizer.radius(4)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Iconsax.information5,
                          size: Sizer.radius(24),
                          color: AppColors.primaryBlue,
                        ),
                        const XBox(8),
                        RichText(
                          text: TextSpan(
                            text: 'Delivered within minutes at ',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.textGray,
                            ),
                            children: [
                              TextSpan(
                                text: vm.transferFee,
                                style: AppTypography.text12.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              TextSpan(
                                text: ' fee',
                                style: AppTypography.text12.copyWith(
                                  color: AppColors.textGray,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        bottomSheet: Container(
          color: AppColors.white,
          padding: EdgeInsets.only(
            left: Sizer.width(20),
            right: Sizer.width(20),
            bottom: Sizer.height(30),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomBtn.solid(
                onTap: () async {
                  if (vm.recipientCurrency?.hasLowBalance == true) {
                    final r = await BsWrapper.bottomSheet(
                      context: context,
                      widget: const LowBalanceAlertSheet(),
                    );
                    if (r == true) {
                      Navigator.pushNamed(
                          context, RoutePath.sendMoneyMethodScreen);
                    }
                    return;
                  }
                  Navigator.pushNamed(context, RoutePath.sendMoneyMethodScreen);
                },
                online: vm.btnEnabled &&
                    context.watch<TransactionVM>().selectedReason != null,
                text: "Continue",
              ),
            ],
          ),
        ),
      );
    });
  }
}
