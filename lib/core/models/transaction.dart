import 'dart:convert';

import 'wallets/currency.dart';

List<Transaction> transactionFromJson(String str) => List<Transaction>.from(
    json.decode(str).map((x) => Transaction.fromJson(x)));

class Transaction {
  Transaction({
    required this.id,
    required this.status,
    required this.amount,
    required this.rate,
    required this.rateFormat,
    required this.currency,
    required this.description,
    required this.fees,
    required this.reference,
    required this.category,
    required this.type,
    required this.runningBalance,
    required this.convertedAmount,
    required this.convertedCurrency,
    required this.destination,
    required this.source,
    required this.interacSecurityAnswer,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? status;
  final String? amount;
  final String? rate;
  final String? rateFormat;
  final Currency? currency;
  final String? description;
  final String? fees;
  final String? reference;
  final String? category; // transfer, conversion
  final String? type;
  final String? runningBalance;
  final dynamic convertedAmount;
  final Currency? convertedCurrency;
  final String? destination;
  final String? source;
  final String? interacSecurityAnswer;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json["id"],
      status: json["status"],
      amount: json["amount"],
      rate: json["rate"],
      rateFormat: json["rate_format"],
      currency:
          json["currency"] == null ? null : Currency.fromJson(json["currency"]),
      description: json["description"],
      fees: json["fees"],
      reference: json["reference"],
      category: json["category"],
      type: json["type"],
      runningBalance: json["running_balance"],
      convertedAmount: json["converted_amount"],
      convertedCurrency: json["converted_currency"] == null
          ? null
          : Currency.fromJson(json["converted_currency"]),
      destination: json["destination"],
      source: json["source"],
      interacSecurityAnswer: json["interac_security_answer"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "status": status,
        "amount": amount,
        "rate": rate,
        "rate_format": rateFormat,
        "currency": currency?.toJson(),
        "description": description,
        "fees": fees,
        "reference": reference,
        "category": category,
        "type": type,
        "running_balance": runningBalance,
        "converted_amount": convertedAmount,
        "converted_currency": convertedCurrency,
        "destination": destination,
        "source": source,
        "interac_security_answer": interacSecurityAnswer,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  String toString() {
    return 'Transaction(id: $id, status: $status, amount: $amount, rate: $rate, rate_format: $rateFormat, currency: $currency, description: $description, fees: $fees, reference: $reference, category: $category, type: $type, runningBalance: $runningBalance, convertedAmount: $convertedAmount, convertedCurrency: $convertedCurrency, destination: $destination, source: $source, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

class TransactionFee {
  String? fee;
  String? method;

  TransactionFee({
    this.fee,
    this.method,
  });

  factory TransactionFee.fromJson(Map<String, dynamic> json) => TransactionFee(
        fee: json["fee"],
        method: json["method"],
      );

  Map<String, dynamic> toJson() => {
        "fee": fee,
        "method": method,
      };
}
